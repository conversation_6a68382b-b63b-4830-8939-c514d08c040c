import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../../auth-service/auth.service';

@Component({
  selector: 'app-callback',
  template: '',
  standalone: true
})
export class CallbackComponent implements OnInit, OnDestroy {
  private subscription: Subscription = new Subscription();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService
  ) { }

  async ngOnInit(): Promise<void> {
    const refreshToken = this.route.snapshot.queryParams['refresh_token'];
    const code = this.route.snapshot.queryParams['code'];

    if (refreshToken) {
      this.handleTokenRefresh(refreshToken);
    } else if (code) {
      this.handleCodeExchange(code);
    } else {
      console.warn('No authorization code or refresh token found in callback URL');
    }
  }

  private handleTokenRefresh(refreshToken: string): void {
    const refreshSub = this.authService.refreshToken(refreshToken).subscribe({
      next: () => {
        console.log('Token refresh successful, redirecting to experience...');
        this.router.navigate(['/experience/main']);
      },
      error: (err) => {
        console.error('Token refresh failed:', err);
        this.router.navigate(['/login']);
      },
    });
    this.subscription.add(refreshSub);
  }

  private handleCodeExchange(code: string): void {
    const exchangeSub = this.authService.exchangeCodeForToken(code, environment.redirectUrl).subscribe({
      next: () => {
        console.log('Token exchange successful, redirecting to experience...');
        this.router.navigate(['/experience/main']);
      },
      error: (err) => {
        console.error('Token exchange failed:', err);
        this.router.navigate(['/login']);
      },
    });
    this.subscription.add(exchangeSub);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
