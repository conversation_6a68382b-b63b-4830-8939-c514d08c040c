import {
  Component,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ButtonComponent, HeaderComponent, HeadingComponent } from '@awe/play-comp-library';
import { ThemeService } from '../../services/theme-service/theme.service';
import { AppConstants } from '../../appConstants';
import { ObserverManager } from '../../utils/subscription-management.util';
import { createLogger } from '../../utils/logger';
import { TokenStorageService } from '../../../authentication/token-storage/token-storage.service';
import { ToastService } from '../../services/toast.service';
import { AuthService } from '../../../authentication/auth-service/auth.service';

@Component({
  selector: 'app-nav-header',
  imports: [HeaderComponent, CommonModule, ButtonComponent, HeadingComponent],
  standalone: true,
  templateUrl: './nav-header.component.html',
  styleUrl: './nav-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NavHeaderComponent implements OnInit, OnDestroy, OnChanges {
 
  showProfileMenu = false;
  themeToggleIcon = '';
  themeMenuIcon = '';
  logoSrc = '';
  private observerManager = new ObserverManager();
  private logger = createLogger('NavHeaderComponent');
  constructor(
    private themeService: ThemeService,
    private authService: AuthService,
    private cdr: ChangeDetectorRef,
    private tokenStorageService: TokenStorageService,
    private router: Router,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.logger.info('🎯 NavHeader component initialized');
    this.updateThemeAssets();
    this.observerManager.createMutationObserver(document.body, () => this.updateThemeAssets(), {
      attributes: true,
      attributeFilter: ['class'],
    });

  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['userProfile']) {
      // Force change detection
      this.cdr.markForCheck();
      this.cdr.detectChanges();
    }
  }

  ngOnDestroy(): void {
    this.observerManager.cleanup();
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.updateThemeAssets();
  }

  getDisplayName() {
    return this.tokenStorageService.getName() || 'User';
  }

  toggleProfileMenu(): void {
    this.showProfileMenu = !this.showProfileMenu;
    this.cdr.markForCheck();
  }

  onLogout(): void {
    try {
      const currentUrl = window.location.origin;
      this.authService.logout(currentUrl).subscribe({
        next: () => {
          this.logger.debug('Logout successful');
        },
        error: error => {
          this.logger.error('Logout failed:', error);
          // Clear tokens locally even if logout API fails
          this.tokenStorageService.clearTokens();
          this.router.navigate(['/login']);
        },
      });
    } catch (error) {
      this.logger.error('Error during logout:', error);
      this.toastService.error('Logout failed');
    }
  }

  onLogin(): void {
    try {
      this.logger.info('Triggering login...');
      const currentUrl = window.location.origin;
      this.authService.login(currentUrl).subscribe({
        next: () => this.logger.debug('Login successful'),
        error: error => this.logger.error('Login failed:', error),
      });
    } catch (error) {
      this.logger.error('Error during login:', error);
      this.toastService.error('Login failed');
    }
  }

  getProfileImage(): string {
    return `${AppConstants.AssetsPath}/user-avatar.svg`;
  }

  getEmail(): string {
    return this.tokenStorageService.getUsername() || '<EMAIL>';
  }

  private updateThemeAssets(): void {
    const currentTheme = this.themeService.getCurrentTheme();
    this.logoSrc = `${AppConstants.AssetsPath}/ascendion-logo-${currentTheme}.svg`;
    this.themeToggleIcon = `${AppConstants.AssetsPath}/theme-toggle-${currentTheme}.svg`;
    this.themeMenuIcon = `${AppConstants.AssetsPath}/menu-${currentTheme}.svg`;
    this.cdr.markForCheck();
  }
}
