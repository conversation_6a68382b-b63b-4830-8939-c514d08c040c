import {
  AfterContentChecked,
  ChangeDetectorRef,
  Component,
  OnInit,
  OnD<PERSON>roy,
  ChangeDetectionStrategy,
} from '@angular/core';
import { RouterOutlet, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { NavHeaderComponent } from './shared/components/nav-header/nav-header.component';
import { AuthService } from './authentication/auth-service/auth.service';
import { AuthTokenService } from './authentication/token-service/auth-token.service';
import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { createLogger } from './shared/utils';
import { UserProfile } from './shared/models/user-profile.model';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, NavHeaderComponent, CommonModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent implements OnInit, AfterContentChecked, OnDestroy {
  title = 'experienceStudio';

  // Navigation and UI state
  public showExpandedNav: boolean = true;

  // Authentication state
  public isLoggedIn: boolean = false;
  public userProfile: UserProfile | undefined = undefined;

  // Token management
  public checkIntervalMs = 4000;
  public tokenCheckInterval: any;
  public subscription = new Subscription();

  // Theme management
  public isThemeLoaded: boolean = false;

  // Debug panel (show in development)
  public showDebugPanel: boolean = true; // Set to false for production

  // Lifecycle management
  private readonly _destroying$ = new Subject<void>();
  private logger = createLogger('AppComponent');

  constructor(
    private cdr: ChangeDetectorRef,
    public authService: AuthService,
    private authTokenService: AuthTokenService
  ) {}

  public ngOnInit(): void {
    // Handle authentication code and token from URL
    this.authTokenService.handleAuthCodeAndToken();
    this.authTokenService.startTokenCheck();
    // Subscribe to authentication state changes from AuthService
    this.authService.authState$.pipe(takeUntil(this._destroying$)).subscribe(isAuthenticated => {
      this.logger.info(
        `Auth state changed: ${isAuthenticated ? 'AUTHENTICATED' : 'NOT AUTHENTICATED'}`
      );
    });
  }
  ngAfterContentChecked(): void {
    this.cdr.detectChanges();
  }
  ngOnDestroy(): void {
    this._destroying$.next();
    this._destroying$.complete();
    this.authTokenService.stopTokenCheck();
    this.subscription.unsubscribe();
  }
}
