import {
  AfterContentChecked,
  ChangeDetectorRef,
  Component,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { RouterOutlet, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { NavHeaderComponent } from './shared/components/nav-header/nav-header.component';
import { AuthService } from './authentication/auth-service/auth.service';
import { TokenStorageService } from './authentication/token-storage/token-storage.service';
import { AuthTokenService } from './authentication/token-service/auth-token.service';
import { ThemeService } from './shared/services/theme-service/theme.service';
import { ToastService } from './shared/services/toast.service';
import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { createLogger } from './shared/utils';
import { UserProfile } from './shared/models/user-profile.model';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, NavHeaderComponent, CommonModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit, AfterContentChecked, OnDestroy {
  title = 'experienceStudio';

  // Navigation and UI state
  public showExpandedNav: boolean = true;

  // Authentication state
  public isLoggedIn: boolean = false;
  public userProfile: UserProfile | undefined = undefined;

  // Token management
  public checkIntervalMs = 4000;
  public tokenCheckInterval: any;
  public subscription = new Subscription();

  // Theme management
  public isThemeLoaded: boolean = false;

  // Debug panel (show in development)
  public showDebugPanel: boolean = true; // Set to false for production

  // Lifecycle management
  private readonly _destroying$ = new Subject<void>();
  private logger = createLogger('AppComponent');

  constructor(
    private cdr: ChangeDetectorRef,
    private router: Router,
    public authService: AuthService,
    private tokenStorageService: TokenStorageService,
    private authTokenService: AuthTokenService,
    private themeService: ThemeService,
    private toastService: ToastService
  ) {}

  public ngOnInit(): void {
    // this.initializeApp();
    // Handle authentication code and token from URL
    this.authTokenService.handleAuthCodeAndToken();
    this.authTokenService.startTokenCheck();
    this.logUserDetails();
    this.loadUserProfile();
  }


  private loadUserProfile(): void {
    try {
      // Get user info from token storage
      const name = this.tokenStorageService.getName();
      const username = this.tokenStorageService.getUsername();

      if (name && username) {
        this.userProfile = {
          displayName: name,
          mail: username,
          userPrincipalName: username,
          photoUrl: undefined,
        };
        this.logger.info('User profile loaded successfully');
      } else {
        this.logger.warn('User profile data not found in token storage');
      }
    } catch (error) {
      this.logger.error('Error loading user profile:', error);
    }
  }

  private logUserDetails(): void {
    try {
      const accessToken = this.tokenStorageService.getAccessToken();
      const refreshToken = this.tokenStorageService.getRefreshToken();
      const name = this.tokenStorageService.getName();
      const username = this.tokenStorageService.getUsername();
      const idToken = this.tokenStorageService.getIdToken();

      this.logger.info(' === USER AUTHENTICATION DETAILS ===');
      this.logger.info(`User Name: ${name || 'Not available'}`);
      this.logger.info(`User Email: ${username || 'Not available'}`);
      this.logger.info(`Access Token: ${accessToken ? 'Present' : 'Missing'}`);
      this.logger.info(`Refresh Token: ${refreshToken ? 'Present' : 'Missing'}`);
      this.logger.info(`ID Token: ${idToken ? 'Present' : 'Missing'}`);
      this.logger.info(
        `Authentication Status: ${this.isLoggedIn ? 'AUTHENTICATED' : 'NOT AUTHENTICATED'}`
      );

      if (this.userProfile) {
        this.logger.info(' User Profile Object:', this.userProfile);
      }

      this.logger.info(' === END USER DETAILS ===');
    } catch (error) {
      this.logger.error(' Error logging user details:', error);
    }
  }

  public checkAuthenticationStatus(): void {
    this.logger.info(' === MANUAL AUTHENTICATION CHECK ===');
    const isAuth = this.authService.isAuthenticated();
    this.logger.info(
      `Current authentication status: ${isAuth ? ' AUTHENTICATED' : 'NOT AUTHENTICATED'}`
    );

    if (isAuth) {
      this.logUserDetails();
    } else {
      this.logger.info('User is not authenticated. No user details available.');
    }
    this.logger.info(' === END MANUAL CHECK ===');
  }



  ngAfterContentChecked(): void {
    this.cdr.detectChanges();
  }

  public onLogout(): void {
    try {
      const currentUrl = window.location.origin;
      this.authService.logout(currentUrl).subscribe({
        next: () => {
          this.logger.debug('Logout successful');
          this.isLoggedIn = false;
          this.userProfile = undefined;
        },
        error: (error) => {
          this.logger.error('Logout failed:', error);
          // Clear tokens locally even if logout API fails
          this.tokenStorageService.clearTokens();
          this.isLoggedIn = false;
          this.userProfile = undefined;
          this.router.navigate(['/login']);
        }
      });
    } catch (error) {
      this.logger.error('Error during logout:', error);
      this.toastService.error('Logout failed');
    }
  }

  public onLogin(): void {
    try {
      this.logger.info('Triggering login...');
      this.router.navigate(['/login']);
    } catch (error) {
      this.logger.error('Error triggering login:', error);
      this.toastService.error('Failed to initiate login');
    }
  }

  ngOnDestroy(): void {
    this._destroying$.next();
    this._destroying$.complete();
    this.authTokenService.stopTokenCheck();
    this.subscription.unsubscribe();
  }
}
