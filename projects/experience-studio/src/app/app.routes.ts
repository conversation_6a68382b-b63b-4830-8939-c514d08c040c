import { Routes } from '@angular/router';
import { CardSelectionGuard } from './shared/guards/card-selection.guard';
import { AuthGuard } from './authentication/auth-gaurd/auth-gaurd.service';

export const routes: Routes = [
  {
    path: 'login',
    loadComponent: () => import('./authentication/components/login/login.component').then(m => m.LoginComponent)
  },
  {
    path: 'callback',
    loadComponent: () => import('./authentication/components/callback/callback.component').then(m => m.CallbackComponent)
  },
  {
    path: 'experience',
    children: [
      {
        path: '',
        loadChildren: () => import('./pages/experience-routing').then(m => m.EXPERIENCE_ROUTES),
        data: { preload: true }
      },
      // Legacy route - keep for backward compatibility
      {
        path: 'prompt',
        loadComponent: () =>
          import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
            m => m.PromptContentComponent
          ),
          canActivate: [AuthGuard],
      },
      // Legacy route - keep for backward compatibility
      {
        path: 'code-preview',
        loadComponent: () =>
          import('./shared/components/code-window/code-window.component').then(
            m => m.CodeWindowComponent
          ),
        canActivate: [AuthGuard],


      },
      // New routes for Generate Application
      {
        path: 'generate-application',
        children: [
          {
            path: 'prompt',
            loadComponent: () =>
              import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
                m => m.PromptContentComponent
              ),
                     canActivate: [AuthGuard],

            data: { cardType: 'Generate Application' }
          },
          {
            path: 'code-preview',
            loadComponent: () =>
              import('./shared/components/code-window/code-window.component').then(
                m => m.CodeWindowComponent
              ),
                     canActivate: [AuthGuard],

            data: { cardType: 'Generate Application' }
          }
        ]
      },
      // New routes for Generate UI Design
      {
        path: 'generate-ui-design',
        children: [
          {
            path: 'prompt',
            loadComponent: () =>
              import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
                m => m.PromptContentComponent
              ),
            canActivate: [CardSelectionGuard],
            data: { cardType: 'Generate UI Design' }
          },
          {
            path: 'code-preview',
            loadComponent: () =>
              import('./shared/components/code-window/code-window.component').then(
                m => m.CodeWindowComponent
              ),
                      canActivate: [AuthGuard],

            data: { cardType: 'Generate UI Design' }
          }
        ]
      }
    ]
  },
  {
    path: '**',
    redirectTo: 'experience',
  },
];
