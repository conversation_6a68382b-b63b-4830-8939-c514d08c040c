import { provideHttpClient, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';
import {
  ApplicationConfig,
  importProvidersFrom,
  inject,
  provideZoneChangeDetection,
  SecurityContext,
} from '@angular/core';
import { provideRouter, withPreloading } from '@angular/router';
import { provideMarkdown, MARKED_OPTIONS } from 'ngx-markdown';
// import { MsalModule, MsalService, MSAL_INSTANCE } from '@azure/msal-angular';
// import { IPublicClientApplication, PublicClientApplication } from '@azure/msal-browser';
// import { msalConfig } from './config/auth.config';
import { provideAnimations } from '@angular/platform-browser/animations';
import { LOGGER_CONFIG, LoggerService } from './shared/utils/logger';
import { environment } from '../environments/environment';
import { CacheInterceptor } from './shared/interceptors/cache.interceptor';
import { HttpErrorInterceptor } from './shared/interceptors/http-error.interceptor';
import { SelectivePreloadingStrategy } from './shared/strategies/selective-preloading-strategy';

import { routes } from './app.routes';
import { InterceptorService } from './authentication/auth-interceptor/interceptor.service';
// import { InterceptorService } from './authentication/auth-interceptor/interceptor.service';
// 
// function MSALInstanceFactory(): IPublicClientApplication {
//   return new PublicClientApplication(msalConfig);
// }

export const appConfig: ApplicationConfig = {
  providers: [
    // Configure HTTP client with interceptors
    // Order matters: HttpErrorInterceptor should run first to catch all errors

   provideHttpClient(
      withInterceptors([
        HttpErrorInterceptor,
        CacheInterceptor,  // ⚠️ This is a class, not a function interceptor!
      ])
    ),
    InterceptorService,

    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withPreloading(SelectivePreloadingStrategy)),
    SelectivePreloadingStrategy,
    provideMarkdown({
      sanitize: SecurityContext.HTML,
      markedOptions: {
        provide: MARKED_OPTIONS,
        useValue: {
          gfm: true,
          breaks: true,
          pedantic: false,
        },
      },
    }),

    provideAnimations(), // Add animations provider
    LoggerService,
    {
      provide: LOGGER_CONFIG,
      useValue: {
        enableLogging: !environment.production,
        logLevel: environment.production ? 'error' : 'debug',
        enableConsoleColors: true,
      },
    },
  ],
};
