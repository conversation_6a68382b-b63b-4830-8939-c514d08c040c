import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, tap, BehaviorSubject, catchError, throwError, map } from 'rxjs';
import { TokenStorageService } from '../token-storage/token-storage.service';
import { environment } from '../../../environments/environment';

// Define interfaces for API responses
interface LoginResponse {
  loginUrl: string;
}
interface TokenResponse {
  token_type: string;
  scope: string;
  expires_in: number;
  access_token: string;
  id_token: string;
  refresh_token: string;
  user_name: string;
  email: string;
}
interface TokenPair {
  accessToken: string;
  refreshToken: string;
}
@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private http = inject(HttpClient);
  private tokenStorage = inject(TokenStorageService);
  private apiAuthUrl = environment.apiAuthUrl;

  private readonly header = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  };

  private authStateSubject = new BehaviorSubject<boolean>(this.isAuthenticated());
  public authState$ = this.authStateSubject.asObservable();

  private getAuthHeaders(): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Basic YWRtaW46YWRtaW4xMjM=`,
    });
  }

  login(redirectUrl: string): Observable<LoginResponse> {
    const url = `${this.apiAuthUrl}/auth/login-url?redirectUrl=${redirectUrl}`;
    return this.http.get<LoginResponse>(url, { headers: this.getAuthHeaders() }).pipe(
      tap(({ loginUrl }) => {
        window.location.href = loginUrl;
        console.log('Login URL:', loginUrl);
      })
    );
  }

  exchangeCodeForToken(code: string, redirectUrl: string): Observable<TokenPair> {
    const url = `${this.apiAuthUrl}/auth/token?redirectUrl=${redirectUrl}`;
    return this.http
      .post<TokenResponse>(url, { code: code }, { headers: this.getAuthHeaders() })
      .pipe(
        tap(response => {
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: name,
            email: username,
            id_token: idToken,
          } = response;
          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeInfo(name, username, idToken);
          this.authStateSubject.next(true);
        }),
        map(response => ({
          accessToken: response.access_token,
          refreshToken: response.refresh_token,
        })),
        catchError(error => {
          console.error('Token exchange failed:', error);
          return throwError(() => error);
        })
      );
  }

  refreshToken(refreshToken?: string): Observable<TokenPair> {
    const refreshTokenFromCookies = this.tokenStorage.getRefreshToken();
    const url = `${this.apiAuthUrl}/auth/refresh-token`;
    console.log('URL:', url);

    return this.http
      .post<TokenResponse>(
        url,
        { refreshToken: refreshToken ? refreshToken : refreshTokenFromCookies },
        { headers: this.getAuthHeaders() }
      )
      .pipe(
        tap(response => {
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: name,
            email: username,
            id_token: idToken,
          } = response;
          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeInfo(name, username, idToken);
          this.authStateSubject.next(true);
        }),
        map(response => ({
          accessToken: response.access_token,
          refreshToken: response.refresh_token,
        })),
        catchError(error => {
          console.error('Token refresh failed:', error);
          return throwError(() => error);
        })
      );
  }

  logout(redirectUrl?: string): Observable<any> {
    const idToken = this.tokenStorage.getIdToken();
    const url = `${this.apiAuthUrl}/auth/logout-url?redirectUrl=${redirectUrl}`;

    const headers = this.getAuthHeaders().set('X-ID-TOKEN', idToken || '');

    return this.http.get<any>(url, { headers }).pipe(
      tap(({ logoutUrl }) => {
        this.tokenStorage.clearTokens();
        this.authStateSubject.next(false);
        window.location.href = logoutUrl;
      }),
      catchError(error => {
        console.error('Logout failed:', error);
        return throwError(() => error);
      })
    );
  }

  isAuthenticated(): boolean {
    return !!this.tokenStorage.getAccessToken();
  }
}
